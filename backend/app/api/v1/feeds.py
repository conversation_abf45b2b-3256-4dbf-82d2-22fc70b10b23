"""
动态分享相关API路由
"""

from typing import List, Optional
from fastapi import APIRouter, Depends, HTTPException, Query, status
from sqlalchemy.orm import Session

from app.core.database import get_db
from app.core.auth import get_current_user, get_current_user_required
from app.models.user import User
from app.services.feed_service import FeedService
from app.schemas.feed import (
    FeedCreate, FeedUpdate, FeedResponse, FeedListResponse,
    FeedQueryParams, FeedLikeResponse,
    FeedCommentCreate, FeedCommentResponse, FeedCommentListResponse
)

router = APIRouter(prefix="/feeds", tags=["动态"])


def get_feed_service(db: Session = Depends(get_db)) -> FeedService:
    """获取动态服务实例"""
    return FeedService(db)


@router.get("/health")
async def feeds_health():
    """动态服务健康检查"""
    return {"status": "ok", "service": "feeds"}


@router.post("/", response_model=FeedResponse, status_code=status.HTTP_201_CREATED)
async def create_feed(
    feed_data: FeedCreate,
    current_user: User = Depends(get_current_user_required),
    feed_service: FeedService = Depends(get_feed_service)
):
    """创建动态"""
    try:
        feed = feed_service.create_feed(feed_data, current_user.id)

        # 构建响应数据
        response_data = FeedResponse(
            id=feed.id,
            pet_id=feed.pet_id,
            user_id=feed.user_id,
            content=feed.content,
            images=feed.image_urls,
            mood=feed.mood,
            tags=feed.tag_list,
            location=feed.location,
            likes_count=feed.likes_count,
            comments_count=feed.comments_count,
            shares_count=feed.shares_count,
            views_count=feed.views_count,
            ai_generated_content=feed.ai_generated_content,
            ai_mood_score=feed.ai_mood_score,
            ai_quality_score=feed.ai_quality_score,
            ai_tags=feed.ai_tags,
            status=feed.status,
            is_public=feed.is_public,
            is_featured=feed.is_featured,
            is_ai_generated=feed.is_ai_generated,
            published_at=feed.published_at,
            created_at=feed.created_at,
            updated_at=feed.updated_at,
            is_liked=False,  # 新创建的动态，当前用户未点赞
            pet_name=feed.pet.name if feed.pet else None,
            pet_avatar=feed.pet.avatar_url if feed.pet else None,
            user_name=feed.user.username if feed.user else None
        )

        return response_data

    except ValueError as e:
        raise HTTPException(status_code=400, detail=str(e))
    except Exception as e:
        raise HTTPException(status_code=500, detail="创建动态失败")


@router.get("/", response_model=FeedListResponse)
async def get_feeds(
    page: int = Query(1, ge=1, description="页码"),
    size: int = Query(20, ge=1, le=100, description="每页大小"),
    pet_id: Optional[int] = Query(None, description="宠物ID筛选"),
    user_id: Optional[int] = Query(None, description="用户ID筛选"),
    mood: Optional[str] = Query(None, description="心情筛选"),
    tags: Optional[List[str]] = Query(None, description="标签筛选"),
    is_featured: Optional[bool] = Query(None, description="是否精选筛选"),
    sort_by: str = Query("created_at", description="排序字段"),
    sort_order: str = Query("desc", regex="^(asc|desc)$", description="排序方向"),
    current_user: Optional[User] = Depends(get_current_user),
    feed_service: FeedService = Depends(get_feed_service)
):
    """获取动态列表"""
    try:
        params = FeedQueryParams(
            page=page,
            size=size,
            pet_id=pet_id,
            user_id=user_id,
            mood=mood,
            tags=tags,
            is_featured=is_featured,
            sort_by=sort_by,
            sort_order=sort_order
        )

        user_id_for_query = current_user.id if current_user else None
        feeds, total = feed_service.get_feeds(params, user_id_for_query)

        # 构建响应数据
        feed_responses = []
        for feed in feeds:
            # 检查当前用户是否已点赞
            is_liked = False
            if current_user:
                is_liked = feed_service.check_user_liked(feed.id, current_user.id)

            feed_response = FeedResponse(
                id=feed.id,
                pet_id=feed.pet_id,
                user_id=feed.user_id,
                content=feed.content,
                images=feed.image_urls,
                mood=feed.mood,
                tags=feed.tag_list,
                location=feed.location,
                likes_count=feed.likes_count,
                comments_count=feed.comments_count,
                shares_count=feed.shares_count,
                views_count=feed.views_count,
                ai_generated_content=feed.ai_generated_content,
                ai_mood_score=feed.ai_mood_score,
                ai_quality_score=feed.ai_quality_score,
                ai_tags=feed.ai_tags,
                status=feed.status,
                is_public=feed.is_public,
                is_featured=feed.is_featured,
                is_ai_generated=feed.is_ai_generated,
                published_at=feed.published_at,
                created_at=feed.created_at,
                updated_at=feed.updated_at,
                is_liked=is_liked,
                pet_name=feed.pet.name if feed.pet else None,
                pet_avatar=feed.pet.avatar_url if feed.pet else None,
                user_name=feed.user.username if feed.user else None
            )
            feed_responses.append(feed_response)

        has_more = (page * size) < total

        return FeedListResponse(
            feeds=feed_responses,
            total=total,
            page=page,
            size=size,
            has_more=has_more
        )

    except Exception as e:
        raise HTTPException(status_code=500, detail="获取动态列表失败")


@router.get("/{feed_id}", response_model=FeedResponse)
async def get_feed(
    feed_id: int,
    current_user: Optional[User] = Depends(get_current_user),
    feed_service: FeedService = Depends(get_feed_service)
):
    """获取动态详情"""
    try:
        user_id = current_user.id if current_user else None
        feed = feed_service.get_feed_by_id(feed_id, user_id)

        if not feed:
            raise HTTPException(status_code=404, detail="动态不存在")

        # 检查当前用户是否已点赞
        is_liked = False
        if current_user:
            is_liked = feed_service.check_user_liked(feed.id, current_user.id)

        return FeedResponse(
            id=feed.id,
            pet_id=feed.pet_id,
            user_id=feed.user_id,
            content=feed.content,
            images=feed.image_urls,
            mood=feed.mood,
            tags=feed.tag_list,
            location=feed.location,
            likes_count=feed.likes_count,
            comments_count=feed.comments_count,
            shares_count=feed.shares_count,
            views_count=feed.views_count,
            ai_generated_content=feed.ai_generated_content,
            ai_mood_score=feed.ai_mood_score,
            ai_quality_score=feed.ai_quality_score,
            ai_tags=feed.ai_tags,
            status=feed.status,
            is_public=feed.is_public,
            is_featured=feed.is_featured,
            is_ai_generated=feed.is_ai_generated,
            published_at=feed.published_at,
            created_at=feed.created_at,
            updated_at=feed.updated_at,
            is_liked=is_liked,
            pet_name=feed.pet.name if feed.pet else None,
            pet_avatar=feed.pet.avatar_url if feed.pet else None,
            user_name=feed.user.username if feed.user else None
        )

    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail="获取动态详情失败")


@router.put("/{feed_id}", response_model=FeedResponse)
async def update_feed(
    feed_id: int,
    feed_data: FeedUpdate,
    current_user: User = Depends(get_current_user_required),
    feed_service: FeedService = Depends(get_feed_service)
):
    """更新动态"""
    try:
        feed = feed_service.update_feed(feed_id, feed_data, current_user.id)

        if not feed:
            raise HTTPException(status_code=404, detail="动态不存在或无权限修改")

        # 检查当前用户是否已点赞
        is_liked = feed_service.check_user_liked(feed.id, current_user.id)

        return FeedResponse(
            id=feed.id,
            pet_id=feed.pet_id,
            user_id=feed.user_id,
            content=feed.content,
            images=feed.image_urls,
            mood=feed.mood,
            tags=feed.tag_list,
            location=feed.location,
            likes_count=feed.likes_count,
            comments_count=feed.comments_count,
            shares_count=feed.shares_count,
            views_count=feed.views_count,
            ai_generated_content=feed.ai_generated_content,
            ai_mood_score=feed.ai_mood_score,
            ai_quality_score=feed.ai_quality_score,
            ai_tags=feed.ai_tags,
            status=feed.status,
            is_public=feed.is_public,
            is_featured=feed.is_featured,
            is_ai_generated=feed.is_ai_generated,
            published_at=feed.published_at,
            created_at=feed.created_at,
            updated_at=feed.updated_at,
            is_liked=is_liked,
            pet_name=feed.pet.name if feed.pet else None,
            pet_avatar=feed.pet.avatar_url if feed.pet else None,
            user_name=feed.user.username if feed.user else None
        )

    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail="更新动态失败")


@router.delete("/{feed_id}", status_code=status.HTTP_204_NO_CONTENT)
async def delete_feed(
    feed_id: int,
    current_user: User = Depends(get_current_user_required),
    feed_service: FeedService = Depends(get_feed_service)
):
    """删除动态"""
    try:
        success = feed_service.delete_feed(feed_id, current_user.id)

        if not success:
            raise HTTPException(status_code=404, detail="动态不存在或无权限删除")

        return None

    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail="删除动态失败")


@router.post("/{feed_id}/like", response_model=FeedLikeResponse)
async def like_feed(
    feed_id: int,
    current_user: User = Depends(get_current_user_required),
    feed_service: FeedService = Depends(get_feed_service)
):
    """点赞/取消点赞动态"""
    try:
        is_liked, likes_count = feed_service.like_feed(feed_id, current_user.id)

        return FeedLikeResponse(
            feed_id=feed_id,
            is_liked=is_liked,
            likes_count=likes_count
        )

    except ValueError as e:
        raise HTTPException(status_code=404, detail=str(e))
    except Exception as e:
        raise HTTPException(status_code=500, detail="点赞操作失败")


@router.post("/{feed_id}/comments", response_model=FeedCommentResponse, status_code=status.HTTP_201_CREATED)
async def create_comment(
    feed_id: int,
    comment_data: FeedCommentCreate,
    current_user: User = Depends(get_current_user_required),
    feed_service: FeedService = Depends(get_feed_service)
):
    """添加评论"""
    try:
        comment = feed_service.create_comment(feed_id, comment_data, current_user.id)

        return FeedCommentResponse(
            id=comment.id,
            feed_id=comment.feed_id,
            user_id=comment.user_id,
            parent_id=comment.parent_id,
            content=comment.content,
            is_deleted=comment.is_deleted,
            is_hidden=comment.is_hidden,
            created_at=comment.created_at,
            updated_at=comment.updated_at,
            user_name=comment.user.username if comment.user else None,
            user_avatar=comment.user.avatar_url if comment.user else None,
            is_reply=comment.is_reply,
            reply_count=comment.reply_count
        )

    except ValueError as e:
        raise HTTPException(status_code=400, detail=str(e))
    except Exception as e:
        raise HTTPException(status_code=500, detail="添加评论失败")


@router.get("/{feed_id}/comments", response_model=FeedCommentListResponse)
async def get_feed_comments(
    feed_id: int,
    page: int = Query(1, ge=1, description="页码"),
    size: int = Query(20, ge=1, le=100, description="每页大小"),
    feed_service: FeedService = Depends(get_feed_service)
):
    """获取动态评论列表"""
    try:
        comments, total = feed_service.get_feed_comments(feed_id, page, size)

        comment_responses = []
        for comment in comments:
            # 处理回复列表
            replies = []
            if comment.replies:
                for reply in comment.replies:
                    if not reply.is_deleted:
                        reply_response = FeedCommentResponse(
                            id=reply.id,
                            feed_id=reply.feed_id,
                            user_id=reply.user_id,
                            parent_id=reply.parent_id,
                            content=reply.content,
                            is_deleted=reply.is_deleted,
                            is_hidden=reply.is_hidden,
                            created_at=reply.created_at,
                            updated_at=reply.updated_at,
                            user_name=reply.user.username if reply.user else None,
                            user_avatar=reply.user.avatar_url if reply.user else None,
                            is_reply=reply.is_reply,
                            reply_count=0  # 回复的回复暂不支持
                        )
                        replies.append(reply_response)

            comment_response = FeedCommentResponse(
                id=comment.id,
                feed_id=comment.feed_id,
                user_id=comment.user_id,
                parent_id=comment.parent_id,
                content=comment.content,
                is_deleted=comment.is_deleted,
                is_hidden=comment.is_hidden,
                created_at=comment.created_at,
                updated_at=comment.updated_at,
                user_name=comment.user.username if comment.user else None,
                user_avatar=comment.user.avatar_url if comment.user else None,
                is_reply=comment.is_reply,
                reply_count=comment.reply_count,
                replies=replies
            )
            comment_responses.append(comment_response)

        has_more = (page * size) < total

        return FeedCommentListResponse(
            comments=comment_responses,
            total=total,
            page=page,
            size=size,
            has_more=has_more
        )

    except Exception as e:
        raise HTTPException(status_code=500, detail="获取评论列表失败")


@router.delete("/comments/{comment_id}", status_code=status.HTTP_204_NO_CONTENT)
async def delete_comment(
    comment_id: int,
    current_user: User = Depends(get_current_user_required),
    feed_service: FeedService = Depends(get_feed_service)
):
    """删除评论"""
    try:
        success = feed_service.delete_comment(comment_id, current_user.id)

        if not success:
            raise HTTPException(status_code=404, detail="评论不存在或无权限删除")

        return None

    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail="删除评论失败")